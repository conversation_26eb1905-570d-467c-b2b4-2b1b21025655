<!-- Material Design 3 Dashboard with Vertical Tabs -->
<div class="dashboard-container">
  <!-- Empty State -->
  <div *ngIf="tabService.tabsValue.length === 0" class="empty-state">
    <mat-card class="welcome-card surface-container">
      <mat-card-header>
        <div mat-card-avatar class="welcome-avatar">
          <mat-icon>dashboard</mat-icon>
        </div>
        <mat-card-title>Welcome to NS Drive</mat-card-title>
        <mat-card-subtitle>
          Create your first sync tab to get started
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p class="welcome-description">
          Tabs allow you to run multiple sync operations simultaneously. Each
          tab can have its own profile and run independently in separate
          threads.
        </p>
        <div class="feature-list">
          <div class="feature-item">
            <mat-icon class="feature-icon">sync</mat-icon>
            <span>Real-time synchronization</span>
          </div>
          <div class="feature-item">
            <mat-icon class="feature-icon">cloud</mat-icon>
            <span>Multiple cloud providers</span>
          </div>
          <div class="feature-item">
            <mat-icon class="feature-icon">settings</mat-icon>
            <span>Customizable profiles</span>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button
          mat-raised-button
          color="primary"
          (click)="createTab()"
          class="create-tab-button"
        >
          <mat-icon>add</mat-icon>
          Create First Tab
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Vertical Tab Dashboard -->
  <div *ngIf="tabService.tabsValue.length > 0" class="tab-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-title">
          <mat-icon class="header-icon">sync</mat-icon>
          <div class="title-group">
            <h2 class="title">Sync Operations</h2>
            <p class="subtitle">Manage your file synchronization tasks</p>
          </div>
        </div>
        <button
          mat-fab
          color="primary"
          (click)="createTab()"
          matTooltip="Add new tab"
          class="add-tab-fab"
        >
          <mat-icon>add</mat-icon>
        </button>
      </div>
    </div>

    <!-- Vertical Tab Layout -->
    <div class="vertical-tab-layout">
      <!-- Tab Navigation (Vertical) -->
      <div class="tab-navigation">
        <mat-nav-list class="tab-list">
          <mat-list-item
            *ngFor="let tab of tabService.tabsValue; let i = index"
            class="tab-item"
            [class.active]="getActiveTabIndex() === i"
            [class.running]="tab.currentAction"
            (click)="onTabChange(i)"
          >
            <div matListItemIcon class="tab-icon-container">
              <mat-icon class="tab-icon">{{
                tab.currentAction ? getActionIcon(tab.currentAction) : "tab"
              }}</mat-icon>
              <div
                *ngIf="tab.currentAction"
                class="status-indicator running"
              ></div>
            </div>

            <div matListItemTitle class="tab-content" *ngIf="!tab.isEditing">
              <span class="tab-name">{{ tab.name || "Tab " + (i + 1) }}</span>
              <span class="tab-status" *ngIf="tab.currentAction">{{
                getActionLabel(tab.currentAction)
              }}</span>
            </div>

            <div matListItemTitle class="tab-edit" *ngIf="tab.isEditing">
              <mat-form-field appearance="outline" class="tab-name-field">
                <input
                  matInput
                  #tabNameInput
                  [value]="tab.name"
                  (blur)="finishRenameTab(tab.id, tabNameInput.value)"
                  (keydown.enter)="finishRenameTab(tab.id, tabNameInput.value)"
                  (keydown.escape)="cancelRenameTab(tab.id)"
                  (click)="$event.stopPropagation()"
                  placeholder="Tab name"
                  autofocus
                />
              </mat-form-field>
            </div>

            <div matListItemMeta class="tab-actions" *ngIf="!tab.isEditing">
              <button
                mat-icon-button
                [matMenuTriggerFor]="tabMenu"
                (click)="$event.stopPropagation()"
                class="tab-menu-button"
              >
                <mat-icon>more_vert</mat-icon>
              </button>

              <mat-menu #tabMenu="matMenu">
                <button mat-menu-item (click)="startRenameTab(tab.id)">
                  <mat-icon>edit</mat-icon>
                  <span>Rename</span>
                </button>
                <button
                  mat-menu-item
                  (click)="deleteTab(tab.id)"
                  [disabled]="tabService.tabsValue.length === 1"
                >
                  <mat-icon>delete</mat-icon>
                  <span>Delete</span>
                </button>
              </mat-menu>
            </div>
          </mat-list-item>
        </mat-nav-list>
      </div>

      <!-- Tab Content Area -->
      <div class="tab-content-area">
        <div
          *ngFor="let tab of tabService.tabsValue; let i = index"
          class="tab-content"
          [class.active]="getActiveTabIndex() === i"
        >
          <!-- Control Panel -->
          <mat-card class="control-panel surface-container">
            <mat-card-header>
              <div mat-card-avatar class="control-avatar">
                <mat-icon>settings</mat-icon>
              </div>
              <mat-card-title>Sync Operations</mat-card-title>
              <mat-card-subtitle>
                Choose a profile and sync operation
              </mat-card-subtitle>
            </mat-card-header>

            <mat-card-content class="control-content">
              <!-- Profile Selection -->
              <mat-form-field appearance="outline" class="profile-select">
                <mat-label>Select Profile</mat-label>
                <mat-select
                  [value]="tab.selectedProfileIndex"
                  (selectionChange)="changeProfileTab($event.value, tab.id)"
                >
                  <mat-option [value]="null">No profile selected</mat-option>
                  <mat-option
                    *ngFor="
                      let profile of appService.configInfo$.value.profiles;
                      let idx = index
                    "
                    [value]="idx"
                  >
                    {{ profile.name }}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>folder_shared</mat-icon>
              </mat-form-field>

              <!-- Action Buttons -->
              <div *ngIf="validateTabProfileIndex(tab)" class="action-buttons">
                <button
                  mat-raised-button
                  [color]="
                    tab.currentAction === Action.Pull ? 'warn' : 'primary'
                  "
                  [disabled]="
                    (!validateTabProfileIndex(tab) &&
                      tab.currentAction !== Action.Pull) ||
                    tab.isStopping
                  "
                  (click)="
                    tab.currentAction !== Action.Pull
                      ? pullTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  class="action-button"
                  matTooltip="Download files from remote"
                >
                  <mat-icon>{{
                    tab.isStopping
                      ? "hourglass_empty"
                      : tab.currentAction === Action.Pull
                      ? "stop"
                      : "download"
                  }}</mat-icon>
                  {{
                    tab.isStopping
                      ? "Stopping..."
                      : tab.currentAction === Action.Pull
                      ? "Stop Pull"
                      : "Pull"
                  }}
                </button>

                <button
                  mat-raised-button
                  [color]="
                    tab.currentAction === Action.Push ? 'warn' : 'accent'
                  "
                  [disabled]="
                    !validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.Push
                  "
                  (click)="
                    tab.currentAction !== Action.Push
                      ? pushTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  class="action-button"
                  matTooltip="Upload files to remote"
                >
                  <mat-icon>{{
                    tab.currentAction === Action.Push ? "stop" : "upload"
                  }}</mat-icon>
                  {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
                </button>

                <button
                  mat-raised-button
                  [color]="tab.currentAction === Action.Bi ? 'warn' : 'primary'"
                  [disabled]="
                    !validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.Bi
                  "
                  (click)="
                    tab.currentAction !== Action.Bi
                      ? biTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  class="action-button"
                  matTooltip="Bidirectional sync"
                >
                  <mat-icon>{{
                    tab.currentAction === Action.Bi ? "stop" : "sync"
                  }}</mat-icon>
                  {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
                </button>

                <button
                  mat-raised-button
                  color="warn"
                  [disabled]="
                    !validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.BiResync
                  "
                  (click)="
                    tab.currentAction !== Action.BiResync
                      ? biResyncTab(tab.id)
                      : stopCommandTab(tab.id)
                  "
                  class="action-button"
                  matTooltip="Force resync all files"
                >
                  <mat-icon>{{
                    tab.currentAction === Action.BiResync ? "stop" : "refresh"
                  }}</mat-icon>
                  {{
                    tab.currentAction === Action.BiResync
                      ? "Stop Resync"
                      : "Resync"
                  }}
                </button>
              </div>

              <!-- Status Display -->
              <div *ngIf="tab.currentAction" class="status-display">
                <mat-progress-bar
                  mode="indeterminate"
                  class="progress-bar"
                ></mat-progress-bar>
                <div class="status-chip">
                  <mat-icon class="status-icon">{{
                    getActionIcon(tab.currentAction)
                  }}</mat-icon>
                  <span>{{ getActionLabel(tab.currentAction) }}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Info Cards Grid -->
          <div class="info-grid">
            <!-- Working Directory -->
            <mat-card class="info-card surface-container-low">
              <mat-card-header>
                <div mat-card-avatar class="info-avatar">
                  <mat-icon>folder</mat-icon>
                </div>
                <mat-card-title>Working Directory</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="directory-path">
                  {{ (appService.configInfo$ | async)?.working_dir }}
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Console Output -->
            <mat-card class="console-card surface-container-low">
              <mat-card-header>
                <div mat-card-avatar class="console-avatar">
                  <mat-icon>terminal</mat-icon>
                </div>
                <mat-card-title>Console Output</mat-card-title>
                <div class="header-spacer"></div>
                <button
                  mat-icon-button
                  (click)="clearTabOutput(tab.id)"
                  matTooltip="Clear output"
                  class="clear-button"
                >
                  <mat-icon>clear</mat-icon>
                </button>
              </mat-card-header>
              <mat-card-content>
                <div class="console-output">
                  <pre class="console-text">{{
                    tab.data.join("\n") || "No output yet..."
                  }}</pre>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
