<!-- Material Design 3 Remotes Page -->
<div class="remotes-container mobile-padding">
  <!-- Header Section -->
  <mat-card class="header-card surface-container">
    <mat-card-header>
      <div mat-card-avatar class="header-avatar">
        <mat-icon>cloud_queue</mat-icon>
      </div>
      <mat-card-title>Cloud Storage</mat-card-title>
      <mat-card-subtitle>
        Configure cloud storage connections for sync operations
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-actions class="header-actions">
      <button
        mat-raised-button
        color="primary"
        (click)="openAddRemoteDialog()"
        class="add-remote-button"
      >
        <mat-icon>add</mat-icon>
        Add Remote
      </button>
    </mat-card-actions>
  </mat-card>
  <!-- Remotes List -->
  <div class="remotes-list">
    <mat-card
      *ngIf="(appService.remotes$ | async)?.length; else noRemotes"
      class="remotes-card surface-container"
    >
      <mat-card-header>
        <div mat-card-avatar class="list-avatar">
          <mat-icon>cloud_done</mat-icon>
        </div>
        <mat-card-title>Connected Remotes</mat-card-title>
        <mat-card-subtitle>
          {{ (appService.remotes$ | async)?.length }} remote(s) configured
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <mat-list class="remotes-list-container">
          <mat-list-item
            *ngFor="let remote of appService.remotes$ | async"
            class="remote-item"
          >
            <div matListItemIcon class="remote-icon-container">
              <mat-icon class="remote-icon">{{
                getRemoteIcon(remote.type)
              }}</mat-icon>
            </div>

            <div matListItemTitle class="remote-title">{{ remote.name }}</div>
            <div matListItemLine class="remote-subtitle">
              {{ getRemoteTypeLabel(remote.type) }}
            </div>

            <div matListItemMeta class="remote-actions">
              <button
                mat-icon-button
                color="warn"
                (click)="confirmDeleteRemote(remote)"
                matTooltip="Delete remote"
                class="delete-remote-button"
              >
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </mat-list-item>
        </mat-list>
      </mat-card-content>
    </mat-card>

    <!-- No Remotes State -->
    <ng-template #noRemotes>
      <mat-card class="empty-state-card surface-container">
        <mat-card-header>
          <div mat-card-avatar class="empty-avatar">
            <mat-icon>cloud_off</mat-icon>
          </div>
          <mat-card-title>No Remotes Configured</mat-card-title>
          <mat-card-subtitle>
            Add your first cloud storage connection
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p class="empty-description">
            Remotes allow you to sync files with cloud storage services like
            Google Drive, Dropbox, OneDrive, and more. Connect your cloud
            storage to start syncing files.
          </p>
        </mat-card-content>
        <mat-card-actions>
          <button
            mat-raised-button
            color="primary"
            (click)="openAddRemoteDialog()"
            class="add-first-remote-button"
          >
            <mat-icon>add</mat-icon>
            Add First Remote
          </button>
        </mat-card-actions>
      </mat-card>
    </ng-template>
  </div>
</div>
