<!-- Material Design 3 Profiles Page -->
<div class="profiles-container mobile-padding">
  <!-- Header Section -->
  <mat-card class="header-card surface-container">
    <mat-card-header>
      <div mat-card-avatar class="header-avatar">
        <mat-icon>folder_shared</mat-icon>
      </div>
      <mat-card-title>Sync Profiles</mat-card-title>
      <mat-card-subtitle>
        Configure sync profiles for different directories and remotes
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-actions class="header-actions">
      <button
        mat-raised-button
        color="primary"
        (click)="addProfile()"
        class="add-profile-button"
      >
        <mat-icon>add</mat-icon>
        Add Profile
      </button>
      <div class="action-spacer"></div>
      <button
        mat-raised-button
        color="accent"
        (click)="saveConfigInfo()"
        class="save-button"
      >
        <mat-icon>save</mat-icon>
        {{ saveBtnText$ | async }}
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- Profiles List -->
  <div class="profiles-list">
    <mat-accordion
      *ngIf="
        (appService.configInfo$ | async)?.profiles?.length;
        else noProfiles
      "
      class="profiles-accordion"
    >
      <mat-expansion-panel
        *ngFor="
          let setting of (appService.configInfo$ | async)?.profiles;
          let idx = index
        "
        class="profile-panel surface-container-low"
      >
        <mat-expansion-panel-header class="profile-header">
          <mat-panel-title class="profile-title">
            <div class="profile-icon">
              <mat-icon>folder_shared</mat-icon>
            </div>
            <div class="profile-info">
              <div class="profile-name">
                {{ setting.name || "Untitled Profile" }}
              </div>
              <div class="profile-description">
                {{ getProfileDescription(setting) }}
              </div>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <!-- Profile Form -->
        <div class="profile-form">
          <!-- Basic Information -->
          <mat-card class="form-section surface-container">
            <mat-card-header>
              <div mat-card-avatar class="section-avatar">
                <mat-icon>info</mat-icon>
              </div>
              <mat-card-title>Basic Information</mat-card-title>
            </mat-card-header>
            <mat-card-content class="form-content">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Profile Name</mat-label>
                <input
                  matInput
                  [(ngModel)]="setting.name"
                  placeholder="Enter profile name"
                />
                <mat-icon matSuffix>edit</mat-icon>
              </mat-form-field>
            </mat-card-content>
          </mat-card>

          <!-- Path Configuration -->
          <mat-card>
            <mat-card-header>
              <mat-card-title>Path Configuration</mat-card-title>
              <mat-card-subtitle>
                Configure source and destination paths
              </mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <!-- From Path -->
              <div style="margin-bottom: 24px">
                <h4>Source Path</h4>
                <div style="display: flex; gap: 12px; align-items: flex-start">
                  <mat-form-field
                    appearance="outline"
                    style="min-width: 120px; flex-shrink: 0"
                  >
                    <mat-label>Remote</mat-label>
                    <mat-select
                      [value]="getFromRemote(setting)"
                      (selectionChange)="
                        updateFromPath(
                          setting,
                          $event.value,
                          getFromPath(setting)
                        )
                      "
                    >
                      <mat-option value="">Local</mat-option>
                      <mat-option
                        *ngFor="let remote of appService.remotes$ | async"
                        [value]="remote.name"
                      >
                        {{ remote.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline" style="flex: 1">
                    <mat-label>Path</mat-label>
                    <input
                      matInput
                      [value]="getFromPath(setting)"
                      (input)="
                        updateFromPath(
                          setting,
                          getFromRemote(setting),
                          $any($event.target).value
                        )
                      "
                      placeholder="/source/path"
                    />
                    <mat-icon matSuffix>folder_open</mat-icon>
                  </mat-form-field>
                </div>
              </div>

              <!-- To Path -->
              <div>
                <h4>Destination Path</h4>
                <div style="display: flex; gap: 12px; align-items: flex-start">
                  <mat-form-field
                    appearance="outline"
                    style="min-width: 120px; flex-shrink: 0"
                  >
                    <mat-label>Remote</mat-label>
                    <mat-select
                      [value]="getToRemote(setting)"
                      (selectionChange)="
                        updateToPath(setting, $event.value, getToPath(setting))
                      "
                    >
                      <mat-option value="">Local</mat-option>
                      <mat-option
                        *ngFor="let remote of appService.remotes$ | async"
                        [value]="remote.name"
                      >
                        {{ remote.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline" style="flex: 1">
                    <mat-label>Path</mat-label>
                    <input
                      matInput
                      [value]="getToPath(setting)"
                      (input)="
                        updateToPath(
                          setting,
                          getToRemote(setting),
                          $any($event.target).value
                        )
                      "
                      placeholder="/destination/path"
                    />
                    <mat-icon matSuffix>folder_open</mat-icon>
                  </mat-form-field>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Performance Settings -->
          <mat-card>
            <mat-card-header>
              <mat-card-title>Performance Settings</mat-card-title>
              <mat-card-subtitle>
                Configure parallel transfers and bandwidth limits
              </mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <div
                style="
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 16px;
                  margin-top: 20px;
                "
              >
                <mat-form-field appearance="outline">
                  <mat-label>Parallel Transfers</mat-label>
                  <mat-select [(ngModel)]="setting.parallel">
                    <mat-option
                      *ngFor="let num of getNumberRange(1, 32)"
                      [value]="num"
                    >
                      {{ num }}
                    </mat-option>
                  </mat-select>
                  <mat-icon matSuffix>speed</mat-icon>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Bandwidth Limit (MB/s)</mat-label>
                  <mat-select [(ngModel)]="setting.bandwidth">
                    <mat-option
                      *ngFor="let num of getNumberRange(1, 100)"
                      [value]="num"
                    >
                      {{ num }}
                    </mat-option>
                  </mat-select>
                  <mat-icon matSuffix>network_check</mat-icon>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Actions -->
          <mat-card-actions>
            <button mat-raised-button color="warn" (click)="removeProfile(idx)">
              <mat-icon>delete</mat-icon>
              Delete Profile
            </button>
          </mat-card-actions>
        </div>
      </mat-expansion-panel>
    </mat-accordion>

    <!-- No Profiles State -->
    <ng-template #noProfiles>
      <mat-card style="text-align: center; max-width: 500px; margin: 0 auto">
        <mat-card-header>
          <mat-icon mat-card-avatar>folder_off</mat-icon>
          <mat-card-title>No Profiles Found</mat-card-title>
          <mat-card-subtitle
            >Create your first sync profile to get started</mat-card-subtitle
          >
        </mat-card-header>
        <mat-card-content>
          <p>
            Profiles allow you to configure different sync settings for various
            directories and remotes.
          </p>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="addProfile()">
            <mat-icon>add</mat-icon>
            Create First Profile
          </button>
        </mat-card-actions>
      </mat-card>
    </ng-template>
  </div>
</div>
