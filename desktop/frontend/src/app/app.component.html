<!-- Material Design 3 Mobile-First App Layout -->
<mat-sidenav-container class="app-container">
  <!-- Navigation Drawer -->
  <mat-sidenav
    #drawer
    [mode]="isHandset ? 'over' : 'side'"
    [opened]="!isHandset"
    class="navigation-drawer"
    fixedInViewport="true"
    [disableClose]="!isHandset"
  >
    <!-- Drawer Header -->
    <div class="drawer-header surface-container">
      <div class="app-logo">
        <mat-icon class="logo-icon">storage</mat-icon>
        <span class="app-title">NS Drive</span>
      </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="navigation-menu">
      <mat-nav-list>
        <a
          mat-list-item
          href="javascript:void(0)"
          (click)="openHome(); isHandset && drawer.close()"
          [activated]="(tab$ | async) === 'home'"
          class="nav-item"
        >
          <mat-icon matListItemIcon>dashboard</mat-icon>
          <span matListItemTitle>Dashboard</span>
        </a>

        <a
          mat-list-item
          href="javascript:void(0)"
          (click)="openProfiles(); isHandset && drawer.close()"
          [activated]="(tab$ | async) === 'profiles'"
          class="nav-item"
        >
          <mat-icon matListItemIcon>folder_shared</mat-icon>
          <span matListItemTitle>Sync Profiles</span>
        </a>

        <a
          mat-list-item
          href="javascript:void(0)"
          (click)="openRemotes(); isHandset && drawer.close()"
          [activated]="(tab$ | async) === 'remotes'"
          class="nav-item"
        >
          <mat-icon matListItemIcon>cloud_queue</mat-icon>
          <span matListItemTitle>Cloud Storage</span>
        </a>
      </mat-nav-list>
    </nav>

    <!-- Drawer Footer -->
    <div class="drawer-footer">
      <mat-divider></mat-divider>
      <div class="version-info">
        <mat-icon class="info-icon">info</mat-icon>
        <span>Version 1.0.0</span>
      </div>
    </div>
  </mat-sidenav>

  <!-- Main Content -->
  <mat-sidenav-content class="main-content">
    <!-- App Bar -->
    <mat-toolbar class="app-toolbar" color="primary">
      <button
        mat-icon-button
        (click)="drawer.toggle()"
        class="menu-button mobile-only"
        aria-label="Toggle navigation"
      >
        <mat-icon>menu</mat-icon>
      </button>

      <div class="toolbar-title">
        <mat-icon class="page-icon">{{
          (tab$ | async) === "home"
            ? "dashboard"
            : (tab$ | async) === "profiles"
            ? "folder_shared"
            : (tab$ | async) === "remotes"
            ? "cloud_queue"
            : "storage"
        }}</mat-icon>
        <span class="title-text">
          {{
            (tab$ | async) === "home"
              ? "Dashboard"
              : (tab$ | async) === "profiles"
              ? "Sync Profiles"
              : (tab$ | async) === "remotes"
              ? "Cloud Storage"
              : "NS Drive"
          }}
        </span>
      </div>

      <div class="toolbar-spacer"></div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button
          mat-icon-button
          matTooltip="Settings"
          class="action-button"
          aria-label="Settings"
        >
          <mat-icon>settings</mat-icon>
        </button>

        <button
          mat-icon-button
          matTooltip="Help"
          class="action-button"
          aria-label="Help"
        >
          <mat-icon>help</mat-icon>
        </button>
      </div>
    </mat-toolbar>

    <!-- Page Content Container -->
    <main class="page-content mobile-padding" [ngSwitch]="tab$ | async">
      <app-home *ngSwitchCase="'home'"></app-home>
      <app-profiles *ngSwitchCase="'profiles'"></app-profiles>
      <app-remotes *ngSwitchCase="'remotes'"></app-remotes>
    </main>
  </mat-sidenav-content>
</mat-sidenav-container>
