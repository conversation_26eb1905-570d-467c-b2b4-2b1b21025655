<!-- Modern App Layout with Angular Material -->
<mat-sidenav-container style="height: 100vh; width: 100vw">
  <!-- Navigation Drawer -->
  <mat-sidenav
    #drawer
    [mode]="isHandset ? 'over' : 'side'"
    [opened]="!isHandset"
    style="width: 280px"
    fixedInViewport="true"
    [disableClose]="!isHandset"
  >
    <!-- Drawer Header -->
    <mat-toolbar style="background: transparent; color: inherit">
      <mat-icon style="margin-right: 12px">storage</mat-icon>
      <span style="font-size: 20px; font-weight: 500">NS Drive</span>
    </mat-toolbar>

    <!-- Navigation Menu -->
    <mat-nav-list>
      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openHome(); isHandset && drawer.close()"
        [activated]="(tab$ | async) === 'home'"
        role="button"
        tabindex="0"
      >
        <mat-icon matListItemIcon>dashboard</mat-icon>
        <span matListItemTitle>Dashboard</span>
      </a>

      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openProfiles(); isHandset && drawer.close()"
        [activated]="(tab$ | async) === 'profiles'"
        role="button"
        tabindex="0"
      >
        <mat-icon matListItemIcon>folder_shared</mat-icon>
        <span matListItemTitle>Sync Profiles</span>
      </a>

      <a
        mat-list-item
        href="javascript:void(0)"
        (click)="openRemotes(); isHandset && drawer.close()"
        [activated]="(tab$ | async) === 'remotes'"
        role="button"
        tabindex="0"
      >
        <mat-icon matListItemIcon>cloud_queue</mat-icon>
        <span matListItemTitle>Cloud Storage</span>
      </a>

      <mat-divider style="margin: 16px 0"></mat-divider>
    </mat-nav-list>

    <!-- Drawer Footer -->
    <div
      style="position: absolute; bottom: 0; left: 0; right: 0; padding: 16px"
    >
      <mat-divider></mat-divider>
      <div
        style="
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.6);
          margin-top: 8px;
        "
      >
        <mat-icon style="font-size: 16px; width: 16px; height: 16px"
          >info</mat-icon
        >
        <span>Version 1.0.0</span>
      </div>
    </div>
  </mat-sidenav>

  <!-- Main Content -->
  <mat-sidenav-content
    style="display: flex; flex-direction: column; height: 100vh"
  >
    <!-- App Bar -->
    <mat-toolbar
      color="primary"
      style="
        position: sticky;
        top: 0;
        z-index: 2;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      "
    >
      <button
        mat-icon-button
        (click)="drawer.toggle()"
        [style.display]="!isHandset ? 'none' : 'block'"
        style="margin-right: 8px"
      >
        <mat-icon>menu</mat-icon>
      </button>

      <span style="font-size: 20px; font-weight: 500">
        {{
          (tab$ | async) === "home"
            ? "Dashboard"
            : (tab$ | async) === "profiles"
            ? "Sync Profiles"
            : (tab$ | async) === "remotes"
            ? "Cloud Storage"
            : "NS Drive"
        }}
      </span>

      <span style="flex: 1 1 auto"></span>

      <!-- Action Buttons -->
      <button mat-icon-button matTooltip="Settings">
        <mat-icon>settings</mat-icon>
      </button>

      <button mat-icon-button matTooltip="Help">
        <mat-icon>help</mat-icon>
      </button>
    </mat-toolbar>

    <!-- Page Content Container -->
    <main
      style="flex: 1; overflow: auto; padding: 24px; background-color: #fafafa"
      [ngSwitch]="tab$ | async"
    >
      <app-home *ngSwitchCase="'home'"></app-home>
      <app-profiles *ngSwitchCase="'profiles'"></app-profiles>
      <app-remotes *ngSwitchCase="'remotes'"></app-remotes>
    </main>
  </mat-sidenav-content>
</mat-sidenav-container>
