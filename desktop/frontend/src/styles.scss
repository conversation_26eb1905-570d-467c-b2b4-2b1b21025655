// Material Design 3 Custom Theme
@use "@angular/material" as mat;

// Include the common styles for Angular Material
@include mat.core();

// Define Material Design 3 color palette
$primary-palette: mat.define-palette(mat.$gray-palette, 800, 700, 900);
$accent-palette: mat.define-palette(mat.$blue-gray-palette, 600, 500, 700);
$warn-palette: mat.define-palette(mat.$red-palette, 600, 500, 700);

// Create light theme
$light-theme: mat.define-light-theme(
  (
    color: (
      primary: $primary-palette,
      accent: $accent-palette,
      warn: $warn-palette,
    ),
    typography:
      mat.define-typography-config(
        $font-family: 'Roboto, "Helvetica Neue", sans-serif',
      ),
    density: 0,
  )
);

// Create dark theme
$dark-theme: mat.define-dark-theme(
  (
    color: (
      primary: $primary-palette,
      accent: $accent-palette,
      warn: $warn-palette,
    ),
    typography:
      mat.define-typography-config(
        $font-family: '<PERSON><PERSON>, "Helvetica Neue", sans-serif',
      ),
    density: 0,
  )
);

// Apply light theme by default
@include mat.all-component-themes($light-theme);

// Dark mode support
@media (prefers-color-scheme: dark) {
  @include mat.all-component-colors($dark-theme);
}

// Global styles with Material Design 3 principles
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.87);
}

// Dark mode body styles
@media (prefers-color-scheme: dark) {
  body {
    background-color: #121212;
    color: rgba(255, 255, 255, 0.87);
  }
}

// Custom Material Design 3 overrides
.mat-mdc-form-field {
  &.mat-form-field-appearance-outline {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-form-field-flex {
        .mat-mdc-form-field-outline {
          .mat-mdc-form-field-outline-start,
          .mat-mdc-form-field-outline-notch,
          .mat-mdc-form-field-outline-end {
            border-color: transparent !important;
            border-width: 1px;
          }
        }
      }
    }
  }
}

.mat-mdc-select {
  .mat-mdc-select-trigger {
    .mat-mdc-select-arrow-wrapper {
      .mat-mdc-select-arrow {
        border-color: transparent !important;
      }
    }
  }
}

// Mobile-first responsive design utilities
.mobile-only {
  @media (min-width: 768px) {
    display: none !important;
  }
}

.desktop-only {
  @media (max-width: 767px) {
    display: none !important;
  }
}

.tablet-up {
  @media (max-width: 1023px) {
    display: none !important;
  }
}

.tablet-down {
  @media (min-width: 1024px) {
    display: none !important;
  }
}

// Responsive breakpoints
$mobile-breakpoint: 480px;
$tablet-breakpoint: 768px;
$desktop-breakpoint: 1024px;
$large-desktop-breakpoint: 1200px;

// Responsive mixins
@mixin mobile-only {
  @media (max-width: #{$mobile-breakpoint - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: $tablet-breakpoint) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: $desktop-breakpoint) {
    @content;
  }
}

@mixin large-desktop-up {
  @media (min-width: $large-desktop-breakpoint) {
    @content;
  }
}

// Material Design 3 elevation and surface styles
.surface-container {
  background-color: #f7f2fa;
  border-radius: 12px;
}

@media (prefers-color-scheme: dark) {
  .surface-container {
    background-color: #1d1b20;
  }
}

.surface-container-low {
  background-color: #f1ecf4;
  border-radius: 8px;
}

@media (prefers-color-scheme: dark) {
  .surface-container-low {
    background-color: #1a1c1a;
  }
}

// Android-like card styling
.mat-mdc-card {
  border-radius: 12px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;

  @media (prefers-color-scheme: dark) {
    background-color: #1e1e1e !important;
    color: rgba(255, 255, 255, 0.87) !important;
  }
}

// Android-like button styling
.mat-mdc-raised-button {
  border-radius: 20px !important;
  text-transform: none !important;
  font-weight: 500 !important;
  letter-spacing: 0.1px !important;
}

.mat-mdc-icon-button {
  border-radius: 50% !important;
}

// Android-like input field styling
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: 8px !important;
  }
}

// Improved spacing for mobile
.mobile-padding {
  padding: 16px;

  @media (min-width: 768px) {
    padding: 24px;
  }
}

// App Layout Styles
.app-container {
  height: 100vh;
  width: 100vw;
}

.navigation-drawer {
  width: 280px;

  @include mobile-only {
    width: 100vw;
    max-width: 320px;
  }

  @media (max-width: 767px) {
    width: 100vw;
    max-width: 320px;
  }

  // Ensure drawer content is scrollable on small screens
  .mat-drawer-inner-container {
    @include mobile-only {
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
  }
}

.drawer-header {
  padding: 16px;
  margin: 8px;
  border-radius: 12px;

  .app-logo {
    display: flex;
    align-items: center;
    gap: 12px;

    .logo-icon {
      font-size: 24px;
      color: #6750a4;
    }

    .app-title {
      font-size: 20px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.87);

      @media (prefers-color-scheme: dark) {
        color: rgba(255, 255, 255, 0.87);
      }
    }
  }
}

.navigation-menu {
  padding: 8px;

  .nav-item {
    border-radius: 28px !important;
    margin-bottom: 4px;

    &.secondary {
      opacity: 0.7;
    }

    &:hover {
      background-color: rgba(103, 80, 164, 0.08) !important;
    }

    &[activated="true"] {
      background-color: rgba(103, 80, 164, 0.12) !important;
      color: #6750a4 !important;

      .mat-icon {
        color: #6750a4 !important;
      }
    }
  }

  .nav-divider {
    margin: 16px 0;
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;

  .version-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    margin-top: 8px;

    @media (prefers-color-scheme: dark) {
      color: rgba(255, 255, 255, 0.6);
    }

    .info-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

.main-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.app-toolbar {
  position: sticky;
  top: 0;
  z-index: 2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);

  .menu-button {
    margin-right: 8px;
  }

  .toolbar-title {
    display: flex;
    align-items: center;
    gap: 12px;

    .page-icon {
      font-size: 24px;
      opacity: 0.8;
    }

    .title-text {
      font-size: 20px;
      font-weight: 500;

      @media (max-width: 480px) {
        font-size: 18px;
      }
    }
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }

  .action-buttons {
    display: flex;
    gap: 4px;

    .action-button {
      @media (max-width: 480px) {
        width: 40px;
        height: 40px;
      }
    }
  }
}

.page-content {
  flex: 1;
  overflow: auto;
  background-color: #fafafa;

  @media (prefers-color-scheme: dark) {
    background-color: #121212;
  }
}

// Home Dashboard Styles
.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 24px;

  .welcome-card {
    max-width: 600px;
    text-align: center;

    .welcome-avatar {
      background-color: #6750a4;
      color: white;
    }

    .welcome-description {
      margin: 16px 0;
      color: rgba(0, 0, 0, 0.7);

      @media (prefers-color-scheme: dark) {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .feature-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-top: 24px;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border-radius: 8px;
        background-color: rgba(103, 80, 164, 0.04);

        .feature-icon {
          color: #6750a4;
        }
      }
    }

    .create-tab-button {
      margin-top: 16px;
    }
  }
}

.tab-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
}

.dashboard-header {
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;

    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;

      .header-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
        color: #6750a4;
      }

      .title-group {
        .title {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.87);

          @media (prefers-color-scheme: dark) {
            color: rgba(255, 255, 255, 0.87);
          }

          @media (max-width: 480px) {
            font-size: 20px;
          }
        }

        .subtitle {
          margin: 0;
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;

          @media (prefers-color-scheme: dark) {
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }
    }

    .add-tab-fab {
      @media (max-width: 480px) {
        width: 48px;
        height: 48px;
      }
    }
  }
}

// Vertical Tab Layout
.vertical-tab-layout {
  display: flex;
  gap: 24px;
  height: 100%;
  flex: 1;

  @include mobile-only {
    flex-direction: column;
    gap: 12px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }

  // Ensure proper scrolling on mobile
  @include mobile-only {
    overflow: hidden;
  }
}

.tab-navigation {
  width: 280px;
  flex-shrink: 0;

  @include mobile-only {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  @media (max-width: 768px) {
    width: 100%;
    max-height: 250px;
    overflow-y: auto;
  }

  .tab-list {
    padding: 8px;

    .tab-item {
      border-radius: 12px !important;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(103, 80, 164, 0.08) !important;
      }

      &.active {
        background-color: rgba(103, 80, 164, 0.12) !important;
        border-left: 4px solid #6750a4;

        .tab-icon {
          color: #6750a4 !important;
        }

        .tab-name {
          color: #6750a4 !important;
          font-weight: 500;
        }
      }

      &.running {
        border-left: 4px solid #ff9800;
      }

      .tab-icon-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .tab-icon {
          font-size: 20px;
        }

        .status-indicator {
          position: absolute;
          top: -2px;
          right: -2px;
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.running {
            background-color: #4caf50;
            animation: pulse 2s infinite;
          }
        }
      }

      .tab-content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .tab-name {
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .tab-status {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.6);

          @media (prefers-color-scheme: dark) {
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }

      .tab-edit {
        width: 100%;

        .tab-name-field {
          width: 100%;
        }
      }

      .tab-actions {
        .tab-menu-button {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// Tab Content Area
.tab-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;

  .tab-content {
    display: none;
    flex-direction: column;
    gap: 24px;
    height: 100%;

    &.active {
      display: flex;
    }

    .control-panel {
      .control-avatar {
        background-color: #6750a4;
        color: white;
      }

      .control-content {
        .profile-select {
          width: 100%;
          margin-bottom: 24px;
        }

        .action-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-bottom: 24px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .action-button {
            height: 56px;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;

            @media (max-width: 480px) {
              height: 48px;
              font-size: 14px;
            }
          }
        }

        .status-display {
          margin-top: 24px;

          .progress-bar {
            margin-bottom: 16px;
          }

          .status-chip {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: rgba(76, 175, 80, 0.1);
            border-radius: 20px;
            color: #4caf50;
            font-weight: 500;
            width: fit-content;

            .status-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 24px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .info-card {
        .info-avatar {
          background-color: #6750a4;
          color: white;
        }

        .directory-path {
          font-family: "Courier New", monospace;
          background-color: rgba(0, 0, 0, 0.05);
          padding: 16px;
          border-radius: 8px;
          word-break: break-all;
          font-size: 14px;

          @media (prefers-color-scheme: dark) {
            background-color: rgba(255, 255, 255, 0.05);
          }
        }
      }

      .console-card {
        .console-avatar {
          background-color: #6750a4;
          color: white;
        }

        .header-spacer {
          flex: 1;
        }

        .clear-button {
          width: 32px;
          height: 32px;
        }

        .console-output {
          background-color: #1e1e1e;
          color: #d4d4d4;
          padding: 16px;
          border-radius: 8px;
          font-family: "Courier New", monospace;
          font-size: 13px;
          line-height: 1.4;
          max-height: 400px;
          overflow: auto;

          @media (prefers-color-scheme: dark) {
            background-color: #0d1117;
            color: #e6edf3;
          }

          .console-text {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
          }
        }
      }
    }
  }
}

// Profiles Page Styles
.profiles-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header-card {
  .header-avatar {
    background-color: #6750a4;
    color: white;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    .action-spacer {
      flex: 1;

      @media (max-width: 480px) {
        display: none;
      }
    }

    .add-profile-button,
    .save-button {
      @media (max-width: 480px) {
        flex: 1;
        min-width: 120px;
      }
    }
  }
}

.profiles-list {
  flex: 1;

  .profiles-accordion {
    .profile-panel {
      margin-bottom: 16px;
      border-radius: 12px !important;

      .profile-header {
        .profile-title {
          display: flex;
          align-items: center;
          gap: 12px;
          width: 100%;

          .profile-icon {
            color: #6750a4;
          }

          .profile-info {
            flex: 1;

            .profile-name {
              font-weight: 500;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.87);

              @media (prefers-color-scheme: dark) {
                color: rgba(255, 255, 255, 0.87);
              }
            }

            .profile-description {
              font-weight: normal;
              font-style: italic;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.6);
              margin-top: 4px;

              @media (prefers-color-scheme: dark) {
                color: rgba(255, 255, 255, 0.6);
              }
            }
          }
        }
      }

      .profile-form {
        padding: 16px 0;
        display: flex;
        flex-direction: column;
        gap: 24px;

        .form-section {
          .section-avatar {
            background-color: #6750a4;
            color: white;
          }

          .form-content {
            margin-top: 16px;

            .full-width {
              width: 100%;
            }

            .path-section {
              margin-bottom: 24px;

              &:last-child {
                margin-bottom: 0;
              }

              .path-title {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.87);

                @media (prefers-color-scheme: dark) {
                  color: rgba(255, 255, 255, 0.87);
                }
              }

              .path-inputs {
                display: flex;
                gap: 12px;
                align-items: flex-start;

                @media (max-width: 768px) {
                  flex-direction: column;
                  gap: 16px;
                }

                .remote-select {
                  min-width: 140px;
                  flex-shrink: 0;

                  @media (max-width: 768px) {
                    width: 100%;
                    min-width: unset;
                  }
                }

                .path-input {
                  flex: 1;

                  @media (max-width: 768px) {
                    width: 100%;
                  }
                }
              }
            }

            .performance-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 16px;
              margin-top: 16px;

              @media (max-width: 768px) {
                grid-template-columns: 1fr;
                gap: 16px;
              }

              .performance-field {
                width: 100%;
              }
            }
          }
        }

        .profile-actions {
          display: flex;
          justify-content: flex-end;
          padding: 16px 0;

          .delete-button {
            @media (max-width: 480px) {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

.empty-state-card {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;

  .empty-avatar {
    background-color: rgba(103, 80, 164, 0.1);
    color: #6750a4;
  }

  .empty-description {
    margin: 16px 0;
    color: rgba(0, 0, 0, 0.7);

    @media (prefers-color-scheme: dark) {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .create-first-button {
    margin-top: 16px;
  }
}

// Remotes Page Styles
.remotes-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.remotes-list {
  flex: 1;

  .remotes-card {
    .list-avatar {
      background-color: #6750a4;
      color: white;
    }

    .remotes-list-container {
      .remote-item {
        border-radius: 8px;
        margin-bottom: 8px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(103, 80, 164, 0.04);
        }

        .remote-icon-container {
          .remote-icon {
            color: #6750a4;
            font-size: 24px;
          }
        }

        .remote-title {
          font-weight: 500;
          color: rgba(0, 0, 0, 0.87);

          @media (prefers-color-scheme: dark) {
            color: rgba(255, 255, 255, 0.87);
          }
        }

        .remote-subtitle {
          color: rgba(0, 0, 0, 0.6);

          @media (prefers-color-scheme: dark) {
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .remote-actions {
          .delete-remote-button {
            width: 40px;
            height: 40px;

            &:hover {
              background-color: rgba(244, 67, 54, 0.08);
            }
          }
        }
      }
    }
  }

  .empty-state-card {
    text-align: center;
    max-width: 500px;
    margin: 0 auto;

    .empty-avatar {
      background-color: rgba(103, 80, 164, 0.1);
      color: #6750a4;
    }

    .empty-description {
      margin: 16px 0;
      color: rgba(0, 0, 0, 0.7);

      @media (prefers-color-scheme: dark) {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .add-first-remote-button {
      margin-top: 16px;
    }
  }
}

// General improvements for headers and cards
.header-card {
  .header-avatar {
    background-color: #6750a4;
    color: white;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    @media (max-width: 480px) {
      .add-remote-button {
        flex: 1;
        min-width: 120px;
      }
    }
  }
}

// Enhanced card styling for better mobile experience
.mat-mdc-card {
  transition: box-shadow 0.2s ease, transform 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(-1px);
  }
}

// Improved form field styling
.mat-mdc-form-field {
  &.mat-form-field-appearance-outline {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-form-field-flex {
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          .mat-mdc-form-field-outline {
            .mat-mdc-form-field-outline-start,
            .mat-mdc-form-field-outline-notch,
            .mat-mdc-form-field-outline-end {
              border-color: rgba(103, 80, 164, 0.3) !important;
            }
          }
        }

        &.mdc-text-field--focused {
          .mat-mdc-form-field-outline {
            .mat-mdc-form-field-outline-start,
            .mat-mdc-form-field-outline-notch,
            .mat-mdc-form-field-outline-end {
              border-color: #6750a4 !important;
              border-width: 2px !important;
            }
          }
        }
      }
    }
  }
}

// Additional responsive optimizations
@include mobile-only {
  // Increase touch targets on mobile
  .mat-mdc-icon-button {
    width: 44px !important;
    height: 44px !important;
    min-width: 44px !important;
  }

  .mat-mdc-button {
    min-height: 44px !important;
    padding: 0 16px !important;
  }

  // Improve form field spacing on mobile
  .mat-mdc-form-field {
    margin-bottom: 16px;
  }

  // Better card spacing on mobile
  .mat-mdc-card {
    margin-bottom: 16px;
  }

  // Optimize text sizes for mobile
  .mat-mdc-card-title {
    font-size: 18px !important;
  }

  .mat-mdc-card-subtitle {
    font-size: 14px !important;
  }
}

// Touch-friendly improvements
@media (hover: none) and (pointer: coarse) {
  // Remove hover effects on touch devices
  .mat-mdc-card:hover {
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
  }

  // Increase button sizes for better touch interaction
  .mat-mdc-icon-button {
    width: 48px !important;
    height: 48px !important;
  }

  .action-button {
    min-height: 48px !important;
  }
}

// Landscape orientation optimizations for mobile
@media screen and (max-height: 500px) and (orientation: landscape) {
  .vertical-tab-layout {
    flex-direction: row !important;
    gap: 16px !important;
  }

  .tab-navigation {
    width: 200px !important;
    max-height: none !important;
  }

  .dashboard-header {
    .header-content {
      .title-group {
        .title {
          font-size: 18px !important;
        }
      }
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .status-indicator.running {
    animation: none !important;
  }
}

// Print styles
@media print {
  .navigation-drawer,
  .app-toolbar,
  .action-buttons,
  .tab-navigation {
    display: none !important;
  }

  .page-content {
    padding: 0 !important;
    background: white !important;
  }

  .mat-mdc-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
